package com.trinasolar.integration.task;

import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import com.trinasolar.integration.service.impl.DataShareServiceImpl;

import java.util.concurrent.TimeUnit;

/**
 * @className: DataShareSchedule
 * @Description: 应用系统&应用程序数据共享：定时推送版本变更数据到kafka
 * @author: pengshy
 * @date: 2025/9/2 16:50
 */

@Component
@Slf4j
public class DataShareSchedule {

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private DataShareServiceImpl dataShareService;

    @Scheduled(cron = "0 0 0 6 * ?")
    public void executeDataShareIncrement() {
        // 1. 获取分布式锁（使用项目统一Redis前缀）
        RLock taskLock = redissonClient.getLock("tasp:datashare:system:increment:task:lock");

        try {
            // 2. 尝试获取锁（0秒等待，30秒自动释放，防止死锁）
            boolean isLocked = taskLock.tryLock(0, 30, TimeUnit.SECONDS);
            if (isLocked) {
                // 3. 执行任务逻辑（调用现有Service层方法）
                dataShareService.executeDataShareIncrement();
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        } finally {
            // 4. 确保锁释放（只释放当前线程持有的锁）
            if (taskLock.isHeldByCurrentThread()) {
                taskLock.unlock();
            }
        }
    }

    @Scheduled(cron = "0 0 1 6 * ?")
    public void executeDataShareProgramIncrement() {
        // 1. 获取分布式锁（使用项目统一Redis前缀）
        RLock taskLock = redissonClient.getLock("tasp:datashare:program:increment:task:lock");

        try {
            // 2. 尝试获取锁（0秒等待，30秒自动释放，防止死锁）
            boolean isLocked = taskLock.tryLock(0, 30, TimeUnit.SECONDS);
            if (isLocked) {
                // 3. 执行任务逻辑（调用现有Service层方法）
                dataShareService.executeDataShareIncrement();
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        } finally {
            // 4. 确保锁释放（只释放当前线程持有的锁）
            if (taskLock.isHeldByCurrentThread()) {
                taskLock.unlock();
            }
        }
    }

    @Scheduled(cron = "0 0 1 * * ?")
    public void executeDataShareFull() {
        // 1. 获取分布式锁（使用项目统一Redis前缀）
        RLock taskLock = redissonClient.getLock("tasp:datashare:system:full:task:lock");

        try {
            // 2. 尝试获取锁（0秒等待，30秒自动释放，防止死锁）
            boolean isLocked = taskLock.tryLock(0, 30, TimeUnit.SECONDS);
            if (isLocked) {
                // 3. 执行任务逻辑（调用现有Service层方法）
                dataShareService.executeDataShareFull();
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        } finally {
            // 4. 确保锁释放（只释放当前线程持有的锁）
            if (taskLock.isHeldByCurrentThread()) {
                taskLock.unlock();
            }
        }
    }


    @Scheduled(cron = "0 0 2 * * ?")
    public void executeDataShareProgramFull() {
        // 1. 获取分布式锁（使用项目统一Redis前缀）
        RLock taskLock = redissonClient.getLock("tasp:datashare:program:full:task:lock");

        try {
            // 2. 尝试获取锁（0秒等待，30秒自动释放，防止死锁）
            boolean isLocked = taskLock.tryLock(0, 30, TimeUnit.SECONDS);
            if (isLocked) {
                // 3. 执行任务逻辑（调用现有Service层方法）
                dataShareService.executeDataShareFull();
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        } finally {
            // 4. 确保锁释放（只释放当前线程持有的锁）
            if (taskLock.isHeldByCurrentThread()) {
                taskLock.unlock();
            }
        }
    }
}
