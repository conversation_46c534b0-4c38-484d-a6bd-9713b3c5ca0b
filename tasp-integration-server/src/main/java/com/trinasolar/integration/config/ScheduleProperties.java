package com.trinasolar.integration.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * @className: ScheduleProperties
 * @Description: 定时任务配置属性
 * @author: pengshy
 * @date: 2025/9/3
 */
@Data
@Component
@ConfigurationProperties(prefix = "schedule.datashare")
public class ScheduleProperties {
    
    /**
     * 应用系统增量数据共享定时任务cron表达式
     */
    private String systemIncrementCron = "0 0 0 6 * ?";
    
    /**
     * 应用程序增量数据共享定时任务cron表达式
     */
    private String programIncrementCron = "0 0 1 6 * ?";
    
    /**
     * 应用系统全量数据共享定时任务cron表达式
     */
    private String systemFullCron = "0 0 1 * * ?";
    
    /**
     * 应用程序全量数据共享定时任务cron表达式
     */
    private String programFullCron = "0 0 2 * * ?";
}
